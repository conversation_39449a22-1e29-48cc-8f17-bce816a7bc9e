"""
Debug script for plan execution issues
This script helps diagnose the "plan.data" error in BlendPro
"""

import bpy
import json
from core.interaction_engine import get_interaction_engine
from core.multi_step_planner import get_multi_step_planner
from utils.logger import get_logger, setup_logging

def debug_plan_system():
    """Debug the plan execution system"""
    
    # Setup logging
    setup_logging("DEBUG")
    logger = get_logger("BlendPro.Debug")
    
    logger.info("Starting plan system debug...")
    
    # Get engine and planner
    engine = get_interaction_engine()
    planner = get_multi_step_planner()
    
    # Test plan creation
    test_task = "Create a cube and move it up by 2 units"
    logger.info(f"Creating plan for task: {test_task}")
    
    try:
        plan = planner.create_plan(test_task)
        logger.info(f"Plan created successfully with {len(plan.steps)} steps")
        
        # Generate plan ID and store
        import time
        plan_id = f"plan_{int(time.time())}"
        planner.store_plan(plan, plan_id)
        logger.info(f"Plan stored with ID: {plan_id}")
        
        # Check if plan can be retrieved
        retrieved_plan = planner.get_plan(plan_id)
        if retrieved_plan:
            logger.info("Plan retrieved successfully")
        else:
            logger.error("Failed to retrieve stored plan")
            return False
        
        # List all active plans
        active_plans = list(planner._active_plans.keys())
        logger.info(f"Active plans: {active_plans}")
        
        # Test plan execution
        logger.info("Testing plan execution...")
        result = engine.execute_plan(plan_id)
        
        if result.get('error'):
            logger.error(f"Plan execution failed: {result['error']}")
            return False
        else:
            logger.info("Plan execution successful")
            logger.info(f"Generated code length: {len(result.get('code', ''))}")
            return True
            
    except Exception as e:
        logger.exception(f"Error during plan debug: {str(e)}")
        return False

def debug_ui_plan_data():
    """Debug UI plan data handling"""
    
    logger = get_logger("BlendPro.UIDebug")
    
    # Check chat history for plan messages
    if hasattr(bpy.context.scene, 'blendpro_chat_history'):
        chat_history = bpy.context.scene.blendpro_chat_history
        logger.info(f"Found {len(chat_history)} messages in chat history")
        
        plan_messages = []
        for i, message in enumerate(chat_history):
            if hasattr(message, 'is_interactive') and message.is_interactive:
                plan_messages.append(i)
                logger.info(f"Message {i}: Interactive message found")
                
                if hasattr(message, 'plan_data'):
                    try:
                        plan_data = json.loads(message.plan_data)
                        logger.info(f"  - Plan data: {len(plan_data)} steps")
                    except json.JSONDecodeError as e:
                        logger.error(f"  - Invalid plan data: {e}")
                
                if hasattr(message, 'plan_id'):
                    logger.info(f"  - Plan ID: {message.plan_id}")
                else:
                    logger.warning("  - No plan ID found")
        
        logger.info(f"Found {len(plan_messages)} plan messages")
        return len(plan_messages) > 0
    else:
        logger.error("No chat history found")
        return False

def test_plan_approval_operator():
    """Test the plan approval operator directly"""
    
    logger = get_logger("BlendPro.OperatorTest")
    
    # Create a test plan
    test_steps = [
        {
            "step_number": 1,
            "description": "Create a cube",
            "action_type": "create",
            "expected_outcome": "Cube created in scene"
        }
    ]
    
    plan_steps_json = json.dumps(test_steps)
    plan_id = f"test_plan_{int(time.time())}"
    
    logger.info(f"Testing with plan_id: {plan_id}")
    logger.info(f"Plan steps JSON: {plan_steps_json}")
    
    # Test the operator properties
    try:
        # This simulates what happens when the operator is called
        from core.interaction_engine import BLENDPRO_OT_ApprovePlan
        
        # Create operator instance (this is just for testing)
        op = BLENDPRO_OT_ApprovePlan()
        op.plan_steps_json = plan_steps_json
        op.plan_id = plan_id
        
        logger.info("Operator properties set successfully")
        logger.info(f"  - plan_steps_json length: {len(op.plan_steps_json)}")
        logger.info(f"  - plan_id: {op.plan_id}")
        
        return True
        
    except Exception as e:
        logger.exception(f"Error testing operator: {str(e)}")
        return False

if __name__ == "__main__":
    print("=== BlendPro Plan System Debug ===")
    
    print("\n1. Testing plan system...")
    if debug_plan_system():
        print("✓ Plan system working correctly")
    else:
        print("✗ Plan system has issues")
    
    print("\n2. Testing UI plan data...")
    if debug_ui_plan_data():
        print("✓ UI plan data found")
    else:
        print("✗ No UI plan data found")
    
    print("\n3. Testing plan approval operator...")
    if test_plan_approval_operator():
        print("✓ Plan approval operator working")
    else:
        print("✗ Plan approval operator has issues")
    
    print("\n=== Debug Complete ===")
